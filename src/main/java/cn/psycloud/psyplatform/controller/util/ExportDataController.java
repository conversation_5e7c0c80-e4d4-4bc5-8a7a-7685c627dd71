package cn.psycloud.psyplatform.controller.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ZipUtil;
import cn.psycloud.psyplatform.dto.activityroom.ExportActivitySurveyRecordDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.dto.platform.ExportOptLogDto;
import cn.psycloud.psyplatform.dto.platform.OptLogDto;
import cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto;
import cn.psycloud.psyplatform.entity.platform.OptLogEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.service.platform.OptLogService;
import cn.psycloud.psyplatform.service.survey.SurveyRecordService;
import cn.psycloud.psyplatform.util.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  导出
 */
@Slf4j
@RequestMapping("/export")
@Controller
public class ExportDataController {
    @Autowired
    private TestRecordService testRecordService;
    @Autowired
    private TestScoreService testScoreService;
    @Autowired
    private SurveyRecordService surveyRecordService;
    @Autowired
    private OptLogService optLogService;
    @Autowired
    private ActivityService activityService;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return 文件名
     */
    @RequestMapping(value="/test_record",method = RequestMethod.POST)
    @ResponseBody
    public String exportTestRecord(@RequestBody TestRecordDto dto) {
        List<ExportTestRecordDto> listTestRecords = testRecordService.getExportTestRecordList(dto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        String savePath = uploadPath + "/temp/"+ fileName;
        EasyExcel.write(savePath, ExportTestRecordDto.class)
                .sheet("测评记录")
                .doWrite(listTestRecords);
        return fileName;
    }

    /**
     *  导出调查问卷记录：测评任务
     * @param dto 条件
     * @return 文件名
     */
    @RequestMapping(value = "survey_record", method = RequestMethod.POST)
    @ResponseBody
    public String exportSurveyRecord(@RequestBody TaskSurveyDto dto){
        List<ExportSurveyRecordDto> listRecords = surveyRecordService.getExportRecordList(dto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        String savePath = uploadPath + "/temp/"+ fileName;
        EasyExcel.write(savePath, ExportSurveyRecordDto.class)
                .sheet("问卷作答记录")
                .doWrite(listRecords);
        return fileName;
    }

    /**
     *  导出测评数据
     * @param dto 查询条件
     * @return 文件名
     */
    @RequestMapping(value="/test_score",method = RequestMethod.POST)
    @ResponseBody
    public Object exportTestScore(@RequestBody ExportTestScoreDto dto) {
        var result = new JsonResult<>();
        var dataType = dto.getDataType();
        var sheetName = "";
        List<LinkedHashMap<String,Object>> listMap = new ArrayList<>();
        if("test_score".equals(dataType)){
            listMap = testScoreService.exportTestScore(dto);
            sheetName = "因子得分";
        }
        if("test_result".equals(dataType)){
            listMap = testScoreService.exportTestResult(dto);
            sheetName = "测评选项";
        }
        if("test_result_score".equals(dataType)){
            listMap = testScoreService.exportTestResultScore(dto);
            sheetName = "测评选项得分";
        }
        if("survey_result".equals(dataType)){
            listMap = testScoreService.exportSurveyResutl(dto);
            sheetName = "调查问卷结果";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        if(listMap!=null && !listMap.isEmpty()) {
            List<List<Object>> dataList = new ArrayList<List<Object>>();
            var size = listMap.get(0).size();
            String[] arry = new String[size];
            String[] headArry = new String[size];
            for(Map<String,Object> dataMap: listMap){
                List<Object> data = new ArrayList<Object>();
                int i = 0;
                for(Map.Entry<String,Object> entry: dataMap.entrySet()){
                    headArry[i] = entry.getKey();
                    arry[i] = entry.getValue().toString();
                    data.add(entry.getValue());
                    i++;
                }
                dataList.add(data);
            }

            String savePath = uploadPath + "/temp/"+ fileName;
            EasyExcel.write(savePath)
                    .head(ExcelUtil.createdHead(headArry))
                    .sheet(sheetName)
                    .doWrite(dataList);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(fileName);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     *  测评报告导出word
     * @param response 响应对象
     * @param recordId 记录Id
     */
    @RequestMapping(value="/test_report_word",method = RequestMethod.POST)
    @ResponseBody
    public Object exportTestReportWord(HttpServletResponse response, HttpServletRequest request, @RequestParam Integer recordId) {
        var result = new JsonResult<>();
        String folderName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        var fileName = testRecordService.exportTestReportToWord(response,request,recordId,folderName);
        if(!"".equals(fileName)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(fileName);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     *  批量导出测评报告(word)
     * @param dto 条件
     * @param response 响应
     * @param request 请求
     * @return 文件路径
     */
    @RequestMapping(value="/batch_export_report",method = RequestMethod.POST)
    @ResponseBody
    public Object batchExportTestReport(@RequestBody TestRecordDto dto,HttpServletResponse response, HttpServletRequest request) {
        var result = new JsonResult<>();
        String folder = testRecordService.batchExportReport(response,request,dto);
        if(!"".equals(folder)){
            String sourcePath = uploadPath + "/report/" + folder;
            ZipUtil.zip(sourcePath);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(folder+ ".zip");
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     *  导出操作日志记录
     * @param dto 条件
     * @return 集合
     */
    @RequestMapping(value="/optlog",method = RequestMethod.POST)
    @ResponseBody
    public String exportOptLog(@RequestBody OptLogDto dto) {
        List<ExportOptLogDto> listOptLogs = optLogService.getExportList(dto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        String savePath = uploadPath + "/temp/"+ fileName;
        EasyExcel.write(savePath, OptLogEntity.class)
                .sheet("操作日志记录")
                .doWrite(listOptLogs);
        return fileName;
    }

    /**
     *  导出活动的问卷作答结果
     * @param dto 条件
     * @return 结果实体对象
     */
    @RequestMapping(value="/activity_survey_result",method = RequestMethod.POST)
    @ResponseBody
    public Object exportActivitySurveyResult(@RequestBody ExportActivitySurveyRecordDto dto) {
        var result = new JsonResult<>();
        List<LinkedHashMap<String,Object>> listMap = activityService.exportSurveyResult(dto);
        var sheetName = "调查问卷结果";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        var fileName = sdf.format(new Date())+ ".xlsx";
        if(listMap!=null &&!listMap.isEmpty()) {
            List<List<Object>> dataList = new ArrayList<List<Object>>();
            var size = listMap.get(0).size();
            String[] arry = new String[size];
            String[] headArry = new String[size];
            for(Map<String,Object> dataMap: listMap){
                List<Object> data = new ArrayList<>();
                int i = 0;
                for(Map.Entry<String,Object> entry: dataMap.entrySet()){
                    headArry[i] = entry.getKey();
                    arry[i] = entry.getValue().toString();
                    data.add(entry.getValue());
                    i++;
                }
                dataList.add(data);
            }
            String savePath = uploadPath + "/temp/"+ fileName;
            EasyExcel.write(savePath)
                    .head(ExcelUtil.createdNoWrapHead(headArry))
                    .sheet(sheetName)
                    .doWrite(dataList);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(fileName);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     * 导出调查问卷任务的作答结果
     * @param dto 条件
     * @return 结果实体对象
     */
    @RequestMapping(value="/task_survey_result",method = RequestMethod.POST)
    @ResponseBody
    public Object exportTaskSurveyResult(@RequestBody TaskSurveyDto dto){
        var result = new JsonResult<>();
        List<LinkedHashMap<String,Object>> listMap = surveyRecordService.exportTaskSurveyResult(dto);
        var sheetName = "调查问卷结果";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        if(listMap!=null && !listMap.isEmpty()) {
            List<List<Object>> dataList = new ArrayList<List<Object>>();
            var size = listMap.get(0).size();
            String[] arry = new String[size];
            String[] headArry = new String[size];
            for(Map<String,Object> dataMap: listMap){
                List<Object> data = new ArrayList<Object>();
                int i = 0;
                for(Map.Entry<String,Object> entry: dataMap.entrySet()){
                    headArry[i] = entry.getKey();
                    arry[i] = entry.getValue().toString();
                    data.add(entry.getValue());
                    i++;
                }
                dataList.add(data);
            }

            String savePath = uploadPath + "/temp/"+ fileName;
            EasyExcel.write(savePath)
                    .head(ExcelUtil.createdHead(headArry))
                    .sheet(sheetName)
                    .doWrite(dataList);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(fileName);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     *  导出调查问卷记录：问卷调查任务
     * @param dto 条件
     * @return 文件名
     */
    @RequestMapping(value = "task_survey_record", method = RequestMethod.POST)
    @ResponseBody
    public Object exportTaskSurveyRecord(@RequestBody TaskSurveyDto dto){
        var result = new JsonResult<>();
        List<ExportSurveyRecordDto> listRecords = surveyRecordService.getExportTaskSurveyRecord(dto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        String savePath = uploadPath + "/temp/"+ fileName;
        EasyExcel.write(savePath, ExportSurveyRecordDto.class)
                .sheet("问卷作答记录")
                .doWrite(listRecords);
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(fileName);
        return result;
    }

    /**
     * 批量导出活动问卷作答记录为Word文档
     * @param dto 导出条件
     * @return 结果实体对象
     */
    @RequestMapping(value="/activity_survey_word",method = RequestMethod.POST)
    @ResponseBody
    public Object exportActivitySurveyWord(@RequestBody ExportActivitySurveyRecordDto dto) {
        var result = new JsonResult<>();
        try {
            String zipFileName = activityService.exportSurveyToWord(dto);
            if (!"".equals(zipFileName)) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(zipFileName);
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg("导出失败，暂无数据或系统异常");
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("导出失败：" + e.getMessage());
        }
        return result;
    }
}
