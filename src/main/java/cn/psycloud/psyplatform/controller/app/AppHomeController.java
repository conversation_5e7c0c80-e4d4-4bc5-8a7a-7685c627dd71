package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

@RequestMapping("/app/home")
@Controller
public class AppHomeController {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private SysConfigService sysConfigService;

    /**
     *  H5首页
     * @return 视图
     */
    @GetMapping("/index")
    public ModelAndView index(HttpServletRequest request){
        var mv = new ModelAndView();
        var user = (UserDto) request.getSession().getAttribute("user");

        // 获取系统配置信息
        var sysConfigDto = sysConfigService.get();
        mv.addObject("pageData", sysConfigDto);

        if (user != null) {
            // 获取用户最新参与的活动（最多5条）
            var myActivities = activityService.getMyActivities(user.getUserId());
            if (myActivities != null && myActivities.size() > 5) {
                myActivities = myActivities.subList(0, 5);
            }
            mv.addObject("myActivities", myActivities);
        }

        mv.setViewName("app/home/<USER>");
        return mv;
    }


}
