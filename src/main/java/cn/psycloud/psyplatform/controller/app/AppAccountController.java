package cn.psycloud.psyplatform.controller.app;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.wechat.officialAccount.AccessTokenResponse;
import cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.service.anteroom.UserTokenService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.CookieHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 *  H5端账号密码登录页
 */
@Slf4j
@RequestMapping("/app/account")
@Controller
public class AppAccountController {
    @Value("${wechat.appId}")
    private String wechatAppId;
    @Value("${wechat.appSecret}")
    private String wechatAppSecret;
    @Value("${wechat.redirectUri}")
    private String wechatRedirectUrl;
    @Autowired
    private UserService userService;
    @Autowired
    private UserTokenService userTokenService;

    /**
     *  手机号码登录/注册页面
     * @return 视图
     */
    @GetMapping("/mobile_login")
    public String mobileLogin(){
        return "app/account/mobileLogin";
    }

    /**
     *  账号密码登录页面
     * @return 视图
     */
    @GetMapping("/login")
    public String login(){
        return "app/account/login";
    }

    @GetMapping("/bind_loginName")
    public String bindLoginName(){
        return "app/account/bindLoginName";
    }

    @GetMapping("/wx_login")
    public String wxLogin(){
        return "app/account/wxLogin";
    }

    /**
     *  微信登录
     * @return 视图
     */
    @GetMapping("/wechat_login")
    public String wechatLogin(String code, HttpServletResponse response, HttpServletRequest request) {
        if(code == null || StringUtils.isBlank(code)){
            // 生成带来源页面参数的state
            String referer = request.getHeader("Referer");
            String state = referer != null ? URLEncoder.encode(referer) : "/";
            return String.format("redirect:https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect",
                    wechatAppId,
                    wechatRedirectUrl,
                    state);
        }
        else{
            var authUrl = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                    wechatAppId,
                    wechatAppSecret,
                    code);
            // 获取微信接口响应
            String responseJson = HttpUtil.get(authUrl);
            JSONObject responseObj = JSONUtil.parseObj(responseJson);
            // 处理错误响应
            if(responseObj.containsKey("errcode")){
                 responseObj.getStr("errmsg");
            }
            // 处理成功响应
            AccessTokenResponse res = JSONUtil.toBean(responseJson, AccessTokenResponse.class);
            var accessToken = res.getAccess_token();
            var openId = res.getOpenid();
            var getWechatUserInfoBul = String.format("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
                    accessToken,
                    openId);
            responseJson = HttpUtil.get(getWechatUserInfoBul);
            responseObj = JSONUtil.parseObj(responseJson);
            if(responseObj.containsKey("errcode")){
                String errorMsg = responseObj.getStr("errmsg");
                log.error("获取微信接口响应错误：{}", errorMsg);
            }
            request.getSession().setAttribute("wechatUserInfo", responseJson);
            WechatUserEntity wechatUserEntity = JSONUtil.toBean(responseJson, WechatUserEntity.class);
            UserDto userDto = userService.getUserInfoByWechatUnionId(wechatUserEntity.getUnionid());
            String state = request.getParameter("state");
            String returnUrl = "";
            if (state != null && state.contains("returnUrl=")) {
                returnUrl = state.substring(state.indexOf("returnUrl=") + 10);
                log.error("returnUrl={}", returnUrl);
            }
            if(userDto == null || userDto.getUserId() == 0){
                return "redirect:bind_loginName?returnUrl=" + returnUrl;
            }
            else{
                request.getSession().setAttribute("user", userDto);

                var tokenCookie = CookieHelper.getCookie(request, "token");
                if(tokenCookie != null) {
                    userTokenService.deleteByToken(tokenCookie);
                    CookieHelper.removeCookie(request, response, "token");
                }
                String token = DigestUtil.md5Hex(CommonHelper.getGUID());
                CookieHelper.setCookie(response, "token", token , 30);

                userTokenService.deleteByLoginName(userDto.getLoginName());
                var userToken = new UserTokenEntity();
                userToken.setLoginName(userDto.getLoginName());
                userToken.setToken(token);
                userToken.setIpAddress(CommonHelper.getClinetIP(request));
                userTokenService.insertToken(userToken);

                Map<String, String> loginMap = new HashMap<>();
                loginMap.put("lastLoginDate", CommonHelper.getCurrentDate());
                loginMap.put("lastLoginIp", CommonHelper.getClinetIP(request));
                loginMap.put("userId", userDto.getUserId().toString());
                userService.updateLoginDateAndIp(loginMap);

                return "redirect:" + (state != null ? URLDecoder.decode(returnUrl) : "/");
            }

        }
    }
}
