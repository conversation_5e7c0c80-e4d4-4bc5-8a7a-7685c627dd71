package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface TestScoreDao {
    /**
     *  根据测评记录Id删除测评得分
     * @param recordId 测评记录Id
     * @return 影响行数
     */
    int deleteByRecordId(@Param("recordId") Integer recordId);

    /**
     *  根据测评记录Id和因子Id删除测评得分
     * @param map 测评记录Id和因子Id
     * @return 影响行数
     */
    int deleteByRecordIdAndFactorId(Map<String,Integer> map);

    /**
     *  保存测评得分情况
     * @param entity 测评得分实体对象
     * @return 影响行数
     */
    int addTestScore(TestScoreEntity entity);

    /**
     *  计算因子分
     * @param recordId 测评记录id
     * @param qIds 题目id集合
     * @return 因子分
     */
    BigDecimal computeFactorScore(@Param("recordId") Integer recordId,@Param("qIds") String[] qIds);

    /**
     *   计算因子分：父母养育方式问卷(EMBU)
     * @param recordId 测评记录id
     * @param qIds  题目id集合
     * @return 因子分
     */
    List<LinkedHashMap<String, Object>> computeEmbuFactorScore(@Param("recordId") Integer recordId, @Param("qIds") String[] qIds);

    /**
     *  计算复合因子分数
     * @return 复合因子分
     */
    @Select("${sql}")
    BigDecimal getComplexFactorScore(String sql);

    /**
     *  查询得分情况
     * @param dto 测评得分实体对象
     * @return 测评得分集合
     */
    List<TestScoreDto> getTestScoreList(TestScoreDto dto);

    /**
     *  测评得分情况实体对象
     * @param recordId 记录id
     * @return 测评得分情况实体对象
     */
    TestScoreDto getTestScore(@Param("recordId") Integer recordId);

    /**
     *  更新预警级别
     * @param map 参数
     * @return 影响行数
     */
    int updateWarningLevel(Map<String,Integer> map);

    /**
     *  异常结果导出
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    List<TestRecordDto> getRecordListByTestScore(TestScoreDto dto);

    /**
     *  测评数据导出
     * @param dto 条件
     * @return map集合
     */
    List<LinkedHashMap<String,Object>> getExportTestScore(ExportTestScoreDto dto);

    /**
     *  测评选项导出
     * @param dto 条件
     * @return map集合
     */
    List<LinkedHashMap<String,Object>> getExportTestResult(ExportTestScoreDto dto);

    /**
     *  测评选项得分导出
     * @param dto 条件
     * @return map 集合
     */
    List<LinkedHashMap<String, Object>> getExportTestResultScore(ExportTestScoreDto dto);

    /**
     *  问卷调查结果导出
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String, Object>> getExportSurveyTestResult(ExportTestScoreDto dto);

    /**
     *  测评统计中查询因子得分
     * @param map 条件
     * @return 得分情况集合
     */
    List<TestScoreDto> getTestScoreListForStat(Map<String,Object> map);

    /**
     *  更新《心理健康素养》总分
     * @param dto 得分实体类对象
     * @return 影响行数
     */
    int updateXlsyTotalScorer(TestScoreDto dto);
}
