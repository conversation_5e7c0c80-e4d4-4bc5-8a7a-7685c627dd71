<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/dual-list-box/bootstrap-duallistbox.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">量表管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">量表维护</a></li>
                    </ol>
                </div>
                <h4 class="page-title">量表维护</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-sm-8">
                            <h5><i class="fa fa-laptop mr-1"></i> 创建量表</h5>
                        </div>
                        <div class="col-sm-4">
                            <div class="text-right">
                                <button id="btnAddScale" class="btn btn-success btn-sm" type="button"><i class="fa fa-plus "></i> 添加新量表</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="frmScale" class="form-horizontal form-wizard" role="form">
                        <input type="hidden" name="hidstep" id="hidstep" value="s1" />
                        <div class="wizard-steps"></div>
                        <div class="step" id="s1">
                            <span data-icon="fa fa-cogs" data-text="基本信息"></span>
                            <div class="card-title"><i class="fa fa-list mr-1"></i>未创建完成量表列表</div>
                            <table class="table table-striped nowrap" id="tbUndoneScale">
                                <thead>
                                <tr>
                                    <th align="left">量表名称</th>
                                    <th align="left">操作</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="step" id="s2">
                            <span data-icon="fa fa-list-ol" data-text="条目设置"></span>
                            <div class="form-group">
                                <button id="btnAddQ" type="button" class="btn btn-primary btn-sm mr-1"><i class="fa fa-plus mr-1"></i>添加条目</button>
                                <button id="btnAddA" type="button" class="btn btn-warning btn-sm mr-1"><i class="fa fa-pencil-square mr-1"></i>设置答案</button>
                                <button id="btnBatchDelQ" type="button" class="btn btn-danger btn-sm mr-1"><i class="fa fa-trash-o mr-1"></i>删除</button>
                                <button id="btnImportQAndA" type="button" class="btn btn-light btn-sm mr-1"><i class="fa fa-file-excel-o mr-1"></i>导入</button>
                                <button id="btnRefresh" type="button" class="btn btn-light btn-sm"><i class="fa fa-refresh mr-1"></i>刷新</button>
                                <a th:href="@{/template/量表题目和答案导入模板.xlsx}" class="text-primary pull-right text-decoration-underline"><i class="fa fa-download mr-1"></i>导入模板</a>
                            </div>
                            <div class="card-title">
                                <h5><i class="fa fa-list-ol mr-1"></i> 条目列表 <span class="scaleName"></span></h5>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped nowrap" id="tbQ">
                                    <thead>
                                    <tr>
                                        <th style="width:30px;">
                                            <div class="custom-control custom-checkbox">
                                                <input id="chkall" class="custom-control-input check" type="checkbox">
                                                <label class="custom-control-label" for="chkall"></label>
                                            </div>
                                        </th>
                                        <th>排序</th>
                                        <th>条目</th>
                                        <th>题型</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                        <div class="step" id="s3">
                            <span data-icon="fa fa-outdent" data-text="因子设置"></span>
                            <div class="form-group">
                                <button id="btnAddF" type="button" class="btn btn-primary btn-sm"><i class="fa fa-plus mr-1"></i>添加因子</button>
                            </div>
                            <div class="card-title">
                                <h5><i class="fa fa-outdent mr-1"></i> 因子列表 <span class="scaleName"></span></h5>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped nowrap" id="tbFactor">
                                    <thead>
                                    <tr>
                                        <th style="width:30px;" class="text-center">序号</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>计分方式</th>
                                        <th>所含条目/计分公式</th>
                                        <th>效度量表</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="card-footer">
                                <div class="alert alert-primary bg-white text-primary" role="alert">
                                    <i class="fa fa-info-circle mr-1"></i>说明：异常条件按钮显示灰色表示该因子未设置异常条件；红色表示已经设置异常条件。
                                </div>
                            </div>
                        </div>
                        <div class="wizard-actions">
                            <button class="btn btn-outline-secondary btn-sm pull-left" type="reset" id="pre"><i class="fa fa-arrow-left"></i> 上一步</button>
                            <button class="btn btn-outline-secondary btn-sm pull-right" type="submit" id="next">下一步 <i class="fa fa-arrow-right"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.基本信息 start-->
    <div id="myModalBaseInfo" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmBaseInfo" role="form" action="" method="post">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-scale-title">设置量表基本信息</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>量表类型</label>
                            <select class="form-control" style="width:100%;" name="scaleType" id="scaleType">
                                <option></option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>量表名称</label>
                            <input type="text" class="form-control" name="scaleName" id="scaleName" value="" />
                            <input type="hidden" name="hidScaleName" id="hidScaleName" value="" />
                        </div>
                        <div class="form-group">
                            <label>量表别名</label>
                            <input type="text" class="form-control" name="scaleAlias" id="scaleAlias" />
                        </div>
                        <div class="form-group">
                            <label>指导语</label>
                            <div id="scaleGuide">
                            </div>
                        </div>
                        <div class="form-group form-inline">
                            <label>答题时间（单位：分钟）</label>
                            <input type="number" class="form-control" name="needTime" id="needTime" autocomplete="off" />
                        </div>
                        <div class="form-group">
                            <label class="mr-2">限制条件<i class="fa fa-info-circle ml-1" data-toggle="tooltip" data-placement="right" title="" data-original-title="设置此项后，用户做量表测试之前会验证个人信息中包含的勾选的条件是否完善。"></i></label>
                            <div class="form-check-inline mt-1">
                                <div class="custom-control custom-checkbox mr-2">
                                    <input type="checkbox" class="custom-control-input" name="limit" id="limit_birth" value="出生年月">
                                    <label class="custom-control-label" for="limit_birth">出生年月</label>
                                </div>
                                <div class="custom-control custom-checkbox mr-2">
                                    <input type="checkbox" class="custom-control-input" name="limit" id="limit_sex" value="性别">
                                    <label class="custom-control-label" for="limit_sex">性别</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" name="limit" id="limit_name" value="姓名">
                                    <label class="custom-control-label" for="limit_name">姓名</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-inline">
                            <label>年龄限制（单位：岁）<i class="fa fa-info-circle ml-1 mr-1" data-toggle="tooltip" data-placement="right" title="" data-original-title="默认0表示不限制被试者年龄"></i></label>
                            <input type="number" class="form-control col-2 mr-1" name="age_limit_s" id="age_limit_s" autocomplete="off" min="0" value="0" />至<input type="number" class="form-control col-2 ml-1" name="age_limit_e" id="age_limit_e" autocomplete="off" min="0" value="0" />
                        </div>
                        <div class="form-group">
                            <label>量表介绍</label>
                            <div id="scaleIntro">
                            </div>
                        </div>
                        <div class="form-group form-inline">
                            <label class="mr-2">是否推荐</label>
                            <input type="checkbox" id="isRecommend" data-switch="danger" />
                            <label for="isRecommend" data-on-label="" data-off-label=""></label>
                        </div>
                        <div class="form-group">
                            <img id="scale_thumbnail" src="" class="img-responsive" style="width:200px;" />
                        </div>
                        <div class="form-group">
                            <label>量表图片</label>
                            <input type="file" name="file" id="txt_file" class="file-loading" />
                            <small class="text-secondary"><i class="fa fa-info-circle mr-1"></i>建议像素大小：380*330px</small>
                            <input id="hidScaleThumbnail" type="hidden" value="" />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveBaseInfo" class="btn btn-primary btn-sm" value="保存" />
                        <input type="reset" hidden />
                    </div>
                </form>
                <input type="hidden" name="hidScaleID" id="hidScaleID" value="0" />
            </div>
        </div>
    </div>
    <!-- modal.基本信息 end-->
    <!-- modal.条目 start-->
    <div id="myModalQ" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmQ" action="#">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-q-title"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>条目类型</label>
                            <select id="qType" name="qType" class="form-control">
                                <option value="1">单选题</option>
                                <option value="2">多选题</option>
                                <option value="3">填空题</option>
                                <option value="4">评分单选题</option>
                                <option value="5">迫选题</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>条目内容</label>
                            <div id="qContent">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveQ" class="btn btn-primary btn-sm" value="保存" />
                        <input id="hidQID" type="hidden" value="0" />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.条目 end-->
    <!-- modal.导入题目和答案 start-->
    <div id="myModalImportQ" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">导入题目和答案</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group form-inline">
                        <label class="col-form-label mr-2">选择表格文件：</label>
                        <input type="file" name="file" id="txt_file_import_q" class="file-loading" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-link" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.导入题目和答案 end-->
    <!-- modal.答案 start-->
    <div id="myModalA" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">设置答案</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="frmA" action="#">
                        <input type="hidden" name="hidAID" id="hidAID" value="0" />
                        <input type="hidden" name="hidAno" id="hidAno" value="0" />
                        <div class="form-group">
                            <label>选中的条目：</label>
                            <input id="selQIds" type="text" class="form-control-plaintext" />
                        </div>
                        <div class="form-group">
                            <label>答案</label>
                            <input type="text" name="aContent" id="aContent" class="form-control" autocomplete="off" placeholder="" />
                        </div>
                        <div class="form-group">
                            <label>分数</label>
                            <input type="text" name="aScore" id="aScore" class="form-control" autocomplete="off" placeholder="" />
                        </div>
                        <div class="form-group">
                            <input type="submit" name="btnSaveA" id="btnSaveA" value="保存" class="btn btn-sm btn-primary" />
                            <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        </div>
                    </form>
                    <table class="table table-striped" id="tbA">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>答案</th>
                            <th>分值</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.答案 end-->
    <!-- modal.因子设置 start-->
    <div id="myModalF" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmF" action="#">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-f-title"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="hidFactorID" id="hidFactorID" value="0" />
                        <div class="form-group">
                            <label>因子类型</label>
                            <select class="form-control" name="factorType" id="factorType">
                                <option value="1">普通因子</option>
                                <option value="2">复合因子</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>因子名称</label>
                            <input type="text" name="factorName" id="factorName" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label>因子简称</label>
                            <input type="text" name="factorShortName" id="factorShortName" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label>英文名称</label>
                            <input type="text" name="factorEN" id="factorEN" class="form-control" />
                        </div>
                        <div class="form-group" id="wrapper-qids">
                            <label>选择条目</label>
                            <div class="select">
                                <select multiple="multiple" name="duallistbox" id="qids" class="duallistbox col-lg-12"></select>
                            </div>
                        </div>
                        <div class="form-group hide" id="wrapper-compute">
                            <label>因子公式<i class="fa fa-info-circle ml-1" data-toggle="tooltip" data-placement="right" title="" data-original-title="选择因子后再选择运算符号，示例：{因子1}+ {因子2}..."></i></label>
                            <input type="text" name="compute" id="compute" class="form-control" readonly />
                            <div class="card-body bg-white border mt-2 form-inline">
                                <label class=" mr-1">选择因子</label>
                                <select id="selSingelFactor" class="form-control col-4 mr-2">
                                </select>
                                <button id="btnPlus" class="btn btn-light mr-1" type="button">+</button>
                                <button id="btnSubtract" class="btn btn-light mr-1" type="button">-</button>
                                <button id="btnMultiply" class="btn btn-light mr-1" type="button">*</button>
                                <button id="btnDivide" class="btn btn-light mr-1" type="button">/</button>
                                <button id="btnBack" class="btn btn-light mr-1" type="button">退格</button>
                            </div>
                            <input type="hidden" id="hidCompute" value="" />
                        </div>
                        <div class="form-group">
                            <label for="formula">计分方式</label>
                            <select class="form-control" name="formula" id="formula">
                                <option value="">请选择</option>
                                <option value="1">原始分</option>
                                <option value="2">平均分</option>
                                <option value="3">Z分</option>
                                <option value="4">T分</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>最小分值</label>
                            <input type="text" name="minVal" id="minVal" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label>最大分值</label>
                            <input type="text" name="maxVal" id="maxVal" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label>平均值</label>
                            <input type="text" name="avgVal" id="avgVal" class="form-control" value="0" />
                        </div>
                        <div class="form-group">
                            <label>标准差</label>
                            <input type="text" name="standardVal" id="standardVal" class="form-control" value="0" />
                        </div>
                        <div class="form-group">
                            <label>效度量表</label>
                            <div class="custom-control custom-switch pl-0">
                                <input type="checkbox" id="isLie" data-switch="danger" />
                                <label for="isLie" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveF" class="btn btn-primary btn-sm" value="保存" />
                        <input type="reset" id="resetF" hidden />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.因子设置 end-->
    <!-- modal.结果解释 start-->
    <div id="myModalFE" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-fe-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="frmFE" action="#">
                        <input type="hidden" name="hidFEID" id="hidFEID" value="0" />
                        <div class="form-group mb-3">
                            <label class="col-form-label">起始分（含等号）</label>
                            <input type="number" name="startVal" id="startVal" class="form-control" />
                        </div>
                        <div class="form-group mb-3">
                            <label class="col-form-label">结束分（含等号）</label>
                            <input type="number" name="endVal" id="endVal" class="form-control" />
                        </div>
                        <div class="form-group mb-3">
                            <label class="col-form-label">预警级别</label>
                            <select id="warningLevel" class="form-control" style="width:100%;">
                                <option value="0">无预警级别</option>
                                <option value="1">绿码</option>
                                <option value="2">蓝码</option>
                                <option value="3">黄码</option>
                                <option value="4">橙码</option>
                                <option value="5">红码</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="col-form-label">因子解释</label>
                            <div id="factorExplain"></div>
                        </div>
                        <div class="form-group mb-3 justify-content-start row">
                            <div class="col-8">
                                <input type="submit" name="btnSaveFE" id="btnSaveFE" value="保存" class="btn btn-primary btn-sm" />
                                <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive">
                        <table class="table table-striped" id="tbFE">
                            <thead>
                            <tr>
                                <th>起始分</th>
                                <th>结束分</th>
                                <th>预警级别</th>
                                <th>解释</th>
                                <th style="width:100px;">操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.结果解释 end-->
    <!-- modal.异常条件 start-->
    <div id="myModalAC" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-ac-title"></h5>
                </div>
                <div class="modal-body">
                    <form id="frmAC" action="#">
                        <div class="form-group form-inline">
                            <label class="mr-3">性别</label>
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" id="sexCondition-m" name="sexCondition" class="custom-control-input" value="男">
                                <label class="custom-control-label" for="sexCondition-m">男</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="sexCondition-f" name="sexCondition" class="custom-control-input" value="女">
                                <label class="custom-control-label" for="sexCondition-f">女</label>
                            </div>
                        </div>
                        <div class="form-group form-inline">
                            <label class="mr-3">年龄</label>
                            <select class="form-control mr-2" style="width:120px; padding-right:5px;" name="ageCondition" id="ageCondition">
                                <option value="0">选择条件</option>
                                <option value="1">></option>
                                <option value="2">>=</option>
                                <option value="3"><</option>
                                <option value="4"><=</option>
                                <option value="5">=</option>
                            </select>
                            <input type="text" class="form-control" name="ageVal" id="ageVal" style="width:80px;" placeholder="年龄值" />
                        </div>
                        <div class="form-group form-inline">
                            <label class="mr-2">异常值</label>
                            <select class="form-control mr-2" style="width:120px;" name="scoreCondition" id="scoreCondition">
                                <option value="0">选择条件</option>
                                <option value="1">></option>
                                <option value="2">>=</option>
                                <option value="3"><</option>
                                <option value="4"><=</option>
                                <option value="5">=</option>
                            </select>
                            <input type="text" class="form-control" name="scoreVal" id="scoreVal" style="width:80px;" placeholder="临界值" />
                        </div>
                        <div class="form-group">
                            <input type="button" name="btnSaveAC" id="btnSaveAC" value="保存" class="btn btn-primary btn-sm mr-1" />
                            <button type="button" class="btn btn-light btn-sm" id="btnACClose">关闭</button>
                        </div>
                        <table class="table table-centered" id="tbAC">
                            <thead class="bg-light">
                            <tr>
                                <th>性别</th>
                                <th>年龄</th>
                                <th>异常条件</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.异常条件 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/dual-list-box/jquery.bootstrap-duallistbox.js}"></script>
    <script th:src="@{/static/js/vendor/jquery-ui-1.10.4.min.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.wizard.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let editor_ScaleIntro;
        let editor_ScaleGuide;
        let editor_QContent;
        let editor_FactorExplain;
        let qIds = "";
        let selNum = 0;
        $(function () {
            initPage();
            numberInput("#needTime");
            numberInput("#ageVal");
            //下一步
            $("#next").click(function () {
                let sid = $("#hidScaleID").val();
                let step = $("#hidstep").val();
                if (step === "s1") {
                    $("#hidstep").val("s2");
                    if (sid === "0") {
                        layer.msg('请选择一个量表再进行下一步', { icon: 2, time: 2000 });
                        location.reload();
                    }
                    initQList();
                    $("#btnAddScale").hide();
                }
                if (step === "s2") {
                    $("#hidstep").val("s3");
                    initFList();
                }
                if (step === "s3") {
                    $("#next").html("完成");
                }
            });
            //上一步
            $("#pre").click(function () {
                let step = $("#hidstep").val();
                if (step === "s2") {
                    initUndoneScale();
                    $("#hidstep").val("s1");
                }
                if (step === "s3") {
                    initQList()
                    $("#hidstep").val("s2");
                }
                if ($("#hidstep").val() === "s1") {
                    $("#btnAddScale").show();
                    initUndoneScale();
                }
                $("#next").html("下一步");
            });
            /*设置量表基本信息 start*/
            $("#btnAddScale").click(function () {
                resetForm();
                $("#myModalBaseInfo").modal();
            });
            //继续
            $("#tbUndoneScale").on('click', '.forward', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidScaleID").val(data.id);
                $(".scaleName").html("【" + data.scaleName + "】");
                $("#btnAddScale").hide();
                $("#next").click();
            });
            //修改
            $("#tbUndoneScale").on('click', '.update', function () {
                resetForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidScaleID").val(data.id);
                $("#scaleType").val(data.scaleTypeId);
                $("#scaleName").val(data.scaleName);
                $("#hidScaleName").val(data.scaleName);
                $("#scaleAlias").val(data.scaleAlias);
                editor_ScaleIntro.setData(data.scaleIntro);
                editor_ScaleGuide.setData(data.scaleGuide);
                $("#needTime").val(data.needTime);
                let array_limit = data.testLimit.split(",");
                $.each(array_limit, function (i, item) {
                    $("input[name='limit'][value='" + $.trim(item) +"']").attr("checked", "checked");
                });
                let ageLimit = data.ageLimit.split(',');
                $("#age_limit_s").val(ageLimit[0]);
                $("#age_limit_e").val(ageLimit[1]);
                if (data.isRecommend === 1) {
                    $("#isRecommend").attr("checked", true);
                }
                else {
                    $("#isRecommend").attr("checked", false);
                }
                $("#scale_thumbnail").attr("src", data.thumbnail === "" ? "/static/images/nopic.png" : "/static/upload/scale/thumbnail/" + data.thumbnail);
                $("#hidScaleThumbnail").val(data.thumbnail);
                $("#myModalBaseInfo").modal();
            });
            //删除
            $("#tbUndoneScale").on('click', '.delete', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/measuringroom/scale/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#tbUndoneScale").on('click', '.done', function () {
                let data = oTable.row($(this).parents('tr')).data();
                layer.confirm('确定将量表状态设置成完成吗？', { icon: 0, title: '提示信息' }, function () {
                    let jsonObj = { scaleId: data.id, state: 1 };
                    $.post("/measuringroom/scale/done", jsonObj, function (res) {
                        if (res.resultCode === 200) {
                            layer.msg("操作成功", { icon: 1, time: 2000 });
                            oTable.draw();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    });
                });
            });
            $("#frmBaseInfo").validate({
                //ignore: 'hidden',
                rules: {
                    scaleType: {
                        required: true
                    },
                    scaleName: {
                        required: true,
                        remote: {
                            type: "post",
                            url: "/measuringroom/scale/verify_scale_name",
                            dataType: "text",
                            data: {
                                newScaleName: function () {
                                    return $("#scaleName").val();
                                },
                                originalScaleName: function () {
                                    return $("#hidScaleName").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === "true") {
                                    return false;
                                }
                                else
                                    return true;
                            }
                        }
                    },
                    needTime: {
                        required: true
                    }
                },
                messages: {
                    scaleType: {
                        required: "请选择量表类型"
                    },
                    scaleName: {
                        required: "请填写量表名称",
                        remote: "量表名称已存在"
                    },
                    needTime: {
                        required: "请填写量表答题所需时间"
                    }
                },
                submitHandler: function () {
                    let chklimit = "";
                    $("input[name='limit']").each(function () {
                        if ($(this).is(":checked") === true) {
                            chklimit += $(this).attr('value') + ',';
                        }
                    });
                    let jsonObj = {
                        id: $("#hidScaleID").val(),
                        scaleName: $.trim($("#scaleName").val()),
                        scaleAlias: $("#scaleAlias").val(),
                        scaleTypeId: $("#scaleType").val(),
                        scaleIntro: editor_ScaleIntro.getData(),
                        scaleGuide: editor_ScaleGuide.getData(),
                        needTime: $("#needTime").val(),
                        testLimit: chklimit,
                        ageLimit: $("#age_limit_s").val() + ',' + $("#age_limit_e").val(),
                        isRecommend: $("#isRecommend").prop("checked") ? 1 : 0,
                        thumbnail: $("#hidScaleThumbnail").val()
                    };
                    let url = $("#hidScaleID").val() === "0" ? "/measuringroom/scale/add_scale" : "/measuringroom/scale/update_scale";
                    $("#btnSaveBaseInfo").val("请稍后……");
                    $("#btnSaveBaseInfo").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveBaseInfo").val("保存");
                            $("#btnSaveBaseInfo").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#myModalBaseInfo").modal('hide');
                                    oTable.draw();
                                    $(".scaleName").html("【" + $.trim($("#scalename").val()) + "】");
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            /*设置量表基本信息 end */
            /*条目及答案 start*/
            //添加条目
            $("#btnAddQ").click(function () {
                $("#hidQID").val("0");
                $("#qType").val("1");
                editor_QContent.setData("");
                $("#modal-q-title").html("添加条目")
                $("#myModalQ").modal();
            });
            //修改条目
            $("#tbQ").on('click', '.editQ', function () {
                $("#qType").val("1");
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidQID").val(data.id);
                $("#qType").val(data.qType);
                editor_QContent.setData(data.qContent);
                $("#modal-q-title").html("修改条目")
                $("#myModalQ").modal();
            });
            //条目排序
            $("#tbQ").on('click', '.moveUp', function () {
                let data = oTable.row($(this).parents('tr')).data();
                move(data.id, 'up');
            });
            //条目
            $("#tbQ").on('click', '.moveDown', function () {
                let data = oTable.row($(this).parents('tr')).data();
                move(data.id, 'down');
            });
            //保存条目
            $("#frmQ").validate({
                submitHandler: function () {
                    let jsonObj = {
                        id: $("#hidQID").val(),
                        qContent: editor_QContent.getData(),
                        qType: $("#qType").val(),
                        scaleId: $("#hidScaleID").val()
                    };
                    let url = $("#hidQID").val() === "0" ? "/measuringroom/scaleQuestion/add" : "/measuringroom/scaleQuestion/update";
                    $("#btnSaveQ").val("请稍后……");
                    $("#btnSaveQ").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveQ").val("保存");
                            $("#btnSaveQ").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                $("#myModalQ").modal('hide');
                                oTable.draw();
                                layer.closeAll();
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, {icon: 2, time: 2000});
                            }
                        }
                    });
                }
            });
            //批量删除条目
            $("#btnBatchDelQ").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/measuringroom/scaleQuestion/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#btnRefresh").click(function(){
                oTable.draw();
            });
            //导入题目和答案
            $("#btnImportQAndA").click(function () {
                let oFileInput = new ImportQFileInput();
                oFileInput.Init("txt_file_import_q");
                $("#txt_file_import_q").fileinput('clear');
                $("#myModalImportQ").modal();
            });
            //设置答案（单个）
            $("#tbQ").on('click', '.answer', function () {
                $("#aContent").val("");
                $("#aScore").val("");
                $("#hidAID").val("0");
                let data = oTable.row($(this).parents('tr')).data();
                qIds = data.id;
                $("#selQIds").val(qIds);
                selNum = 1;
                initAList();
                $("#myModalA").modal();
            });
            //设置答案（批量）
            $("#btnAddA").click(function () {
                if ($("input[name^='checklist']:checked").length === 0) {
                    layer.msg("没有选择任何条目");
                    return;
                }
                $("#aContent").val("");
                $("#aScore").val("");
                $("#hidAID").val("0");
                //获取选中的条目
                let ids = "";
                let array = [];
                $("input[name='checklist']:checked").each(function () {
                    array.push($(this).val());
                    selNum++;
                });
                ids = array.join(',');
                if (ids === "") {
                    layer.msg('操作失败', { icon: 2, time: 2000 });
                    return;
                }
                qIds = ids;
                $("#selQIds").val(qIds);
                initAList();
                $("#myModalA").modal();
            });
            //保存答案
            $("#frmA").validate({
                rules: {
                    aContent: {
                        required: true
                    },
                    aScore: {
                        required: true, number: true
                    }
                },
                messages: {
                    aContent: {
                        required: "请填写答案"
                    },
                    aScore: {
                        required: "请填写答案分值", number: "格式不正确"
                    }
                },
                submitHandler: function () {
                    let answer = {};
                    answer.id = $("#hidAID").val();
                    answer.aContent = $.trim($("#aContent").val());
                    answer.aScore = $.trim($("#aScore").val());
                    answer.aNo = $("#hidAno").val();
                    $("#btnSaveA").val("请稍后……");
                    $("#btnSaveA").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/measuringroom/scaleQuestion/batch_add_answer?qIds='+qIds,
                        data: JSON.stringify(answer),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveA").val("保存");
                            $("#btnSaveA").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                initAList();
                                oTable.draw();
                                $("#hidAID").val("0");
                                $("#aContent").val("");
                                $("#aScore").val("");
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            //编辑答案
            $("#tbA").on('click', '.editA', function () {
                let data = oTableA.row($(this).parents('tr')).data();
                $("#hidAID").val(data.id);
                $("#hidAno").val(data.aNo);
                $("#aContent").val(data.aContent);
                $("#aScore").val(data.aScore);
            });
            //删除答案
            $("#tbA").on('click', '.delA', function () {
                let data = oTableA.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.aId = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/measuringroom/scaleQuestion/delete_answer", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                initAList();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            /*条目及答案 end*/

            /*因子设置 start */
            $("#factorType").change(function () {
                if ($(this).val() == 1) {
                    $("#wrapper-qids").removeClass("hide").addClass("show");
                    $("#wrapper-compute").removeClass("show").addClass("hide");
                }
                else {
                    $("#wrapper-qids").removeClass("show").addClass("hide");
                    $("#wrapper-compute").removeClass("hide").addClass("show");
                    let jsonObj = {};
                    jsonObj.scaleId = $("#hidScaleID").val();
                    jsonObj.factorType = 1;
                    initSelectJson("#selSingelFactor", "/measuringroom/scaleFactor/get_for_select", jsonObj);
                }
            });
            $("#selSingelFactor").change(function () {
                let selFactorValue = $(this).val();
                let selFactorText = $(this).find("option:selected").text();
                if (selFactorValue === '') return;
                let formual = $("#compute").val();
                let hidFormual = $("#hidCompute").val();
                $("#compute").val(formual + '{' + selFactorText + '}');
                $("#hidCompute").val(hidFormual + '{' + $("#selSingelFactor").val() + '}');
            });
            $("#btnPlus").click(function () {
                let formual = $("#compute").val();
                let hidFormual = $("#hidCompute").val();
                $("#compute").val(formual + '+');
                $("#hidCompute").val(hidFormual + '+');
            });
            $("#btnSubtract").click(function () {
                let formual = $("#compute").val();
                let hidFormual = $("#hidCompute").val();
                $("#compute").val(formual + '-');
                $("#hidCompute").val(hidFormual + '-');
            });
            $("#btnMultiply").click(function () {
                let formual = $("#compute").val();
                let hidFormual = $("#hidCompute").val();
                $("#compute").val(formual + '*');
                $("#hidCompute").val(hidFormual + '*');
            });
            $("#btnDivide").click(function () {
                let formual = $("#compute").val();
                let hidFormual = $("#hidCompute").val();
                $("#compute").val(formual + '/');
                $("#hidCompute").val(hidFormual + '/');
            });
            $("#btnBack").click(function () {
                let s = $("#compute").val();
                let hidS = $("#hidCompute").val();
                if (s.length > 0) {
                    if (s.charAt(s.length - 1) === '}') {
                        s = s.substr(0, s.lastIndexOf('{'));
                        hidS = hidS.substr(0, hidS.lastIndexOf('{'));
                    }
                    else {
                        s = s.substr(0, s.length - 1);
                        hidS = hidS.substr(0, hidS.length - 1);
                    }
                }
                $("#compute").val(s);
                $("#hidCompute").val(hidS);
            });
            //添加因子
            $("#btnAddF").click(function () {
                resetFormF();
                $("#modal-f-title").html("添加因子");
                $("#hidFactorID").val("0");
                InitDualListBox("");
                $("#myModalF").modal();
            });
            //修改因子
            $("#tbFactor").on('click', '.editF', function () {
                resetFormF();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidFactorID").val(data.id);
                $("#factorType").val(data.factorType).trigger('change');
                $("#factorName").val(data.factorName);
                $("#factorShortName").val(data.factorShortName);
                $("#factorEN").val(data.factorEn);
                $("#formula").val(data.formulaId);
                InitDualListBox(data.qIds);
                $("#compute").val(data.computeOfName);
                $("#hidCompute").val(data.compute);
                $("#minVal").val(data.minScore);
                $("#maxVal").val(data.maxScore);
                $("#avgVal").val(data.avgScore);
                $("#standardVal").val(data.standardScore);
                if (data.isLie === 1) {
                    $("#isLie").attr("checked", true);
                }
                else {
                    $("#isLie").attr("checked", false);
                }
                $("#modal-f-title").html("设置因子[" + data.factorName + "]");
                $("#myModalF").modal();
            });
            //因子设置
            $("#frmF").validate({
                rules: {
                    factorName: {
                        required: true
                    },
                    formula: {
                        required: true
                    },
                    minVal: {
                        required: true, number: true
                    },
                    maxVal: {
                        required: true, number: true
                    }
                },
                messages: {
                    factorName: {
                        required: "请输入因子名称"
                    },
                    formula: {
                        required: "请选择因子计分方式"
                    },
                    minVal: {
                        required: "请填写最小分值", number: "格式不正确"
                    },
                    maxVal: {
                        required: "请填写最大分值", number: "格式不正确"
                    }
                },
                submitHandler: function () {
                    let jsonObj = {
                        id: $("#hidFactorID").val(),
                        factorType: $("#factorType").val(),
                        scaleId: $("#hidScaleID").val(),
                        factorName: $.trim($("#factorName").val()),
                        factorEn: $.trim($("#factorEN").val()),
                        factorShortName: $.trim($("#factorShortName").val()),
                        formulaId: $("#formula").val(),
                        qIds:$("#qids").val().join(','),
                        compute: $("#hidCompute").val(),
                        minScore: $("#minVal").val(),
                        maxScore: $("#maxVal").val(),
                        avgScore: $("#avgVal").val(),
                        standardScore: $("#standardVal").val(),
                        isLie: $("#isLie").prop("checked") ? 1 : 0
                    };
                    let url = $("#hidFactorID").val() === "0" ? "/measuringroom/scaleFactor/add" : "/measuringroom/scaleFactor/update";
                    $("#btnSaveF").val("请稍后……");
                    $("#btnSaveF").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveF").val("保存");
                            $("#btnSaveF").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                                $("#myModalF").modal("hide");
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            //删除因子
            $("#tbFactor").on('click', '.delF', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.factorId = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/measuringroom/scaleFactor/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //排序
            $("#tbFactor").on('click', '.upF', function () {
                let data = oTable.row($(this).parents('tr')).data();
                moveF(data.id, 'up');
            });
            //排序
            $("#tbFactor").on('click', '.downF', function () {
                let data = oTable.row($(this).parents('tr')).data();
                moveF(data.id, 'down');
            });
            //因子解释
            $("#tbFactor").on('click', '.factorExplain', function () {
                $("#frmFE").get(0).reset();
                editor_FactorExplain.setData('');
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidFactorID").val(data.id);
                $("#hidFEID").val("0");
                initFEList();
                $("#modal-fe-title").html("设置因子解释【" + data.factorName + "】");
                $("#myModalFE").modal();
            });
            //编辑因子解释
            $("#tbFE").on('click', '.editFE', function () {
                $("#hidFEID").val("0");
                let data = oTableFE.row($(this).parents('tr')).data();
                $("#hidFEID").val(data.id);
                $("#startVal").val(data.startValue);
                $("#endVal").val(data.endValue);
                $("#warningLevel").val(data.warningLevel);
                editor_FactorExplain.setData(data.interpretation);
                $("#factorExplain").val(data.interpretation);
            });
            //删除因子解释
            $("#tbFE").on('click', '.delFE', function () {
                let data = oTableFE.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/measuringroom/factorExplain/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                initFEList();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //保存因子解释
            $("#frmFE").validate({
                rules: {
                    startVal: {
                        required: true, number: true
                    },
                    endVal: {
                        required: true, number: true
                    }
                },
                messages: {
                    startVal: {
                        required: "请填写起始分", number: "格式不正确"
                    },
                    endVal: {
                        required: "请填写结束分", number: "格式不正确"
                    }
                },
                submitHandler: function () {
                    let jsonObj = {
                        id: $("#hidFEID").val(),
                        factorId: $("#hidFactorID").val(),
                        startValue: $("#startVal").val(),
                        endValue: $("#endVal").val(),
                        warningLevel: $("#warningLevel").val(),
                        interpretation: editor_FactorExplain.getData()
                    };
                    let url = $("#hidFEID").val() === "0" ? "/measuringroom/factorExplain/add" : "/measuringroom/factorExplain/update";
                    $("#btnSaveFE").val("请稍后…");
                    $("#btnSaveFE").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveFE").val("保存");
                            $("#btnSaveFE").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                initFEList();
                                $("#frmFE").get(0).reset();
                                $("#hidFEID").val("0");
                                editor_FactorExplain.setData('');
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            //异常条件
            $("#tbFactor").on('click', '.ac', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidFactorID").val(data.id);
                $("#modal-ac-title").html("设置异常条件【" + data.factorName + "】");
                initACList();
                $("#myModalAC").modal();
            });
            //删除异常条件
            $("#tbAC").on('click', '.delAC', function () {
                let data = oTableAC.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/measuringroom/factorac/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                initACList();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#btnACClose").click(function () {
                oTable.draw();
                $("#myModalAC").modal("hide");
            });
            $("#btnSaveAC").click(function () {
                let sexVal = $('input[name="sexCondition"]:checked').val();
                let ageCondition = $("#ageCondition").val();
                let ageVal = $.trim($("#ageVal").val());
                let scoreCondition = $("#scoreCondition").val();
                let scoreVal = $.trim($("#scoreVal").val());
                if (sexVal == undefined && ageVal === "" && scoreVal === "") {
                    return false;
                }
                let jsonObj = {};
                jsonObj.factorId = $("#hidFactorID").val();
                jsonObj.ageCondition = ageCondition;
                jsonObj.ageValue = ageVal;
                jsonObj.sexCondition = sexVal;
                jsonObj.scoreCondition = scoreCondition;
                jsonObj.scoreValue = scoreVal;

                $("#btnSaveAC").val("保存中…");
                $("#btnSaveAC").attr("Disabled", true);
                $.ajax({
                    type: 'POST',
                    url: '/measuringroom/factorac/add',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnSaveAC").val("保存");
                        $("#btnSaveAC").attr("Disabled", false);
                        if (res.resultCode === 200) {
                            initACList();
                            $("#frmAC").get(0).reset();
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
        });
        let numberInput = function (ctrlName) {
            //限制键盘只能按数字键、小键盘数字键、退格键
            $(ctrlName).keydown(function (e) {
                let code = parseInt(e.keyCode);
                if (code >= 96 && code <= 105 || code >= 48 && code <= 57 || code === 8) {
                    return true;
                } else {
                    return false;
                }
            })

            //文本框输入事件,任何非正整数的输入都重置为1''
            $(ctrlName).bind("input propertychange", function () {
                if (isNaN(parseFloat($(this).val())) || parseFloat($(this).val()) <= 0) {
                    $(this).val('');
                }
            })
        };
        let FileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=scale',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-primary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidScaleThumbnail").val(res.resultMsg);
                        $("#scale_thumbnail").attr("src", res.resultMsg === "" ? "/static/images/nopic.png" : "/static/upload/scale/thumbnail/" + res.resultMsg);
                        layer.msg("上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        let ImportQFileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/measuringroom/scaleQuestion/import_question',
                    uploadExtraData: function (previewId, index) {
                        return {
                            scaleId: $("#hidScaleID").val()
                        };
                    },
                    allowedFileExtensions: ['xls', 'xlsx'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning btn-sm", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "导入",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file_import_q").on("fileuploaded", function (event, data, previewId, index) {
                    layer.msg('导入完成', { icon: 1, time: 2000 });
                    $("#myModalImportQ").modal('hide');
                    initQList();
                });
            };
            return oFile;
        };
        let resetForm = function () {
            let oFileInput = new FileInput();
            oFileInput.Init("txt_file");
            $("#frmBaseInfo input").removeClass("error");
            $("#frmBaseInfo textarea").removeClass("error");
            $("label.error").hide();
            $("#frmBaseInfo input[type='reset']").click();
            $("#hidScaleID").val(0);
            $("#isRecommend").attr("checked", false);
            $("input[name='limit']").each(function () {
                $(this).attr("checked", false);
            });
            editor_ScaleIntro.setData("");
            editor_ScaleGuide.setData("");
        };
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAddScale").show() : $("#btnAddScale").hide();
            //向导插件
            $("#frmScale").formwizard({
                formPluginEnabled: true,
                validationEnabled: false,
                focusFirstInput: true,
                formOptions: {
                    success: function (data) {
                        complete();
                    },
                    resetForm: false
                },
                disableUIStyles: true,
                showSteps: true, //show the step
                vertical: true //activate vertical wizard
            });
            //未创建完成的量表
            initUndoneScale();
            initSelect("#scaleType", "/measuringroom/scaletype/get_for_select", "");
            ClassicEditor
                .create(document.querySelector('#scaleIntro'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_ScaleIntro = editor;
                });
            ClassicEditor
                .create(document.querySelector('#scaleGuide'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_ScaleGuide = editor;
                });
            ClassicEditor
                .create(document.querySelector('#qContent'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_QContent = editor;
                });
            ClassicEditor
                .create(document.querySelector('#factorExplain'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_FactorExplain = editor;
                });
        };
        /*S1 未完成量表目录 start */
        let columns_scale = [{ "data": "scaleName", "bSortable": false }];
        let columnDefs_scale = [{
            targets: 1, render: function (data, type, row, meta) {
                let buttons = '<button type ="button" class="btn btn-outline-info btn-sm mr-1 forward"><i class="fa fa-arrow-circle-right mr-1"></i>继续</button>';
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-edit mr-1"></i>基本信息设置</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-danger btn-sm mr-1 delete"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                buttons += '<button type ="button" class="btn btn-outline-success btn-sm mr-1 done"><i class="fa fa-check-circle-o mr-1"></i>完成</button>';
                return buttons;
            }
        }];
        let getQueryCondition_scale = function (data) {
            let param = {};
            param.isDone = 0;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let initUndoneScale = function () {
            $("#tbUndoneScale").bsDataTables({
                columns: columns_scale,
                url: '/measuringroom/scale/list',
                columnDefs: columnDefs_scale,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_scale(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(JSON.parse(res));
                        }
                    });
                }
            });
        };
        /*S1 未完成量表目录 end*/

        /*S2 条目及答案 start */
        let columns_q = [{
            "data": "id","sClass": "text-center",  "render":
                function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                }, "bSortable": false},
            { "data": "qNumber", "sClass": "text-center","bSortable": false },
            { "data": "qContent", "bSortable": false },
            { "data": "qType", "bSortable": false,
                "render": function (data, type, row, meta) {
                    if (data === 1) {
                        return '单选题';
                    }
                    if (data === 2) {
                        return '多选题';
                    }
                    if (data === 3) {
                        return '填空题';
                    }
                    if (data === 4) {
                        return '评分单选题';
                    }
                    if (data === 5) {
                        return '迫选题';
                    }
                }
            }];
        let columnDefs_q = [{
            targets: 4,
            render: function (data, type, row, meta) {
                let buttons = "";
                buttons += '<button class="btn btn-outline-success btn-sm mr-1 moveUp" type="button" title="上移"><i class="fa fa-arrow-circle-o-up mr-1"></i>上移</button>';
                buttons += '<button class="btn btn-outline-success btn-sm mr-1 moveDown" type="button" title="下移"><i class="fa fa-arrow-circle-o-down mr-1"></i>下移</button>';
                buttons += '<button class="btn btn-outline-warning btn-sm editQ mr-1" type="button"><i class="fa fa-edit mr-1"></i>修改</button>';
                buttons += '<button class="btn btn-outline-info btn-sm answer" type="button"><i class="fa fa-check-square-o mr-1"></i>查看答案</button>';
                return buttons;
            }
        }];
        let getQueryCondition_q = function (data) {
            let param = {};
            param.scaleId = $("#hidScaleID").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let initQList = function () {
            $("#tbQ").bsDataTables({
                columns: columns_q,
                url: '/measuringroom/scaleQuestion/list',
                columnDefs: columnDefs_q,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_q(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(JSON.parse(res));
                            $("#chkall").change(function () {
                                //实现全选
                                if ($('#chkall').prop("checked") === true) {
                                    $('.checklist').prop("checked", true);
                                }
                                else {
                                    $('.checklist').prop("checked", false);
                                }
                            });
                        }
                    });
                }
            });
        };
        //条目排序
        let move = function (id, e) {
            let jsonObj = {};
            jsonObj.qId = id;
            jsonObj.flag = e;
            $.post("/measuringroom/scaleQuestion/sort", jsonObj, function (res) {
                if (res.resultCode === 200) {
                    oTable.draw();
                    layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            }, 'json');
        };

        let oTableA = null;
        let initAList = function () {
            if (oTableA != null) {
                oTableA.destroy();
            }
            oTableA = $("#tbA").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "sAjaxSource": "/measuringroom/scaleQuestion/get_all_answers",
                "fnServerData": retrieveDataA, //执行方法
                //配置列要显示的数据
                "columns": [{ "data": "aNo", "bSortable": false },
                    { "data": "aContent", "bSortable": false },
                    { "data": "aScore", "bSortable": false }],
                "columnDefs":[{
                    targets: 3,
                    render: function (data, type, row, meta) {
                        return '<button class="btn btn-outline-warning btn-sm editA mr-1"><i class="fa fa-pencil-square-o mr-1"></i>编辑</button><button class="btn btn-outline-danger btn-sm delA"><i class="fa fa-trash-o mr-1"></i>删除</button>'
                    }
                }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        let getQueryCondition_a = function (data) {
            let param = {};
            param.qId = selNum > 1 ? qIds.split(',')[0] : qIds;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.iDisplayLength = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.iDisplayStart = item.value;
                }
            });
            return param;
        };
        let retrieveDataA = function (sSource, aoData, fnCallback) {
            $.ajax({
                "type": "post",
                "url": sSource,
                "dataType": "json",
                "data": getQueryCondition_a(aoData),
                "success": function (res) {
                    fnCallback(JSON.parse(res));
                }
            });
        };
        /*S2 条目及答案 end*/

        /*S3 因子设置 start*/
        let resetFormF = function () {
            $("#frmF input").removeClass("error");
            $("label.error").hide();
            $("#frmF input[id='resetF']").click();
            $("#hidFactorID").val(0);
            $("#isLie").attr("checked", false);
            $(".select").find("select").remove();
            $(".select").html('<select multiple="multiple" name="duallistbox" id="qids" class="duallistbox col-lg-12"></select>');
        };
        let InitDualListBox = function (selectedDataStr) {
            let jsonObj = { scaleId: $("#hidScaleID").val() };
            $.ajax({
                url: "/measuringroom/scaleQuestion/get_list_for_dual",
                type: 'POST',
                data: jsonObj,
                dataType: "json",
                async: true,
                success: function (res) {
                    $(res).each(function () {
                        let o = document.createElement("option");
                        o.value = this['id'];
                        o.text = this['name'];
                        if ("undefined" != typeof (selectedDataStr) && selectedDataStr != "") {
                            let selectedDataArray = selectedDataStr.split(',');
                            $.each(selectedDataArray, function (i, val) {
                                if (o.value === val) {
                                    o.selected = 'selected';
                                    return false;
                                }
                            });
                        }
                        $("#qids")[0].options.add(o);
                    });
                    $("#qids").bootstrapDualListbox({
                        infotext: '共有条目数 {0}',
                        infotextfiltered: '<span class="badge badge-danger badge-pill">搜索</span> {0} from {1}',
                        infotextempty: '',
                        selectorminimalheight: 100,
                        showfilterinputs: false,
                        filterplaceholder: '',
                        filtertextclear: '显示所有',
                        iconMove: 'fa fa-angle-right s16',
                        iconMoveAll: 'fa fa-angle-double-right s16',
                        iconRemove: 'fa fa-angle-left s16',
                        iconRemoveAll: 'fa fa-angle-double-left s16'
                    });
                }
            });
        };
        let columns_f = [
            { "data": "factorNo", "bSortable": false,"css":"text-center" },
            { "data": "factorName", "bSortable": false },
            {
                "data": "factorType", "bSortable": false, "render":
                    function (data, type, row, meta) {
                        if (data === 1) {
                            return '普通因子';
                        }
                        if (data === 2) {
                            return '复合因子';
                        }
                    }
            },
            {
                "data": "formulaId", "bSortable": false, "render":
                    function (data, type, row, meta) {
                        let label = "";
                        switch (data) {
                            case 1:
                                label = '原始分';
                                break;
                            case 2:
                                label = '平均分';
                                break;
                            case 3:
                                label = 'Z分';
                                break;
                            case 4:
                                lable = 'T分';
                                break;
                        }
                        return label;
                    }
            },
            { "data": "QIds", "bSortable": false },
            {
                "data": "isLie", "bSortable": false,
                render: function (data, type, row, meta) {
                    if (data === 1) {
                        return '<span class="badge badge-danger badge-pill">是</span>';
                    }
                    else {
                        return '<span class="badge badge-success badge-pill">否</span>';
                    }
                }
            }];
        let columnDefs_f = [{
            targets: 6,
            render: function (data, type, row, meta) {
                let buttons = "";
                buttons += '<button class="btn btn-outline-success btn-sm mr-1 upF" type="button" title="上移"><i class="fa fa-arrow-circle-o-up mr-1"></i>上移</button>';
                buttons += '<button class="btn btn-outline-success btn-sm mr-1 downF" type="button" title="下移"><i class="fa fa-arrow-circle-o-down mr-1"></i>下移</button>';
                buttons += '<button class="btn btn-outline-warning btn-sm editF mr-1" type="button"><i class="fa fa-edit mr-1"></i>修改</button>';
                buttons += '<button class="btn btn-outline-danger btn-sm delF mr-1" type="button"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                buttons += '<button class="btn btn-outline-info btn-sm factorExplain mr-1" type="button"><i class="fa fa-clone mr-1"></i>因子解释</button>';
                //异常条件
                let ac_notify_style = row.abnormalConditions.length === 0 ? "btn btn-light btn-sm" : "btn btn-danger btn-sm";
                buttons += '<button class="' + ac_notify_style + ' ac" data-toggle="tooltip" data-placement="bottom" title="该按钮显示灰色表示该因子未设置异常条件;红色表示已经设置异常条件" type="button">异常条件</button>';
                return buttons;
            }
        }];
        let getQueryCondition_f = function (data) {
            let param = {};
            param.scaleId = $("#hidScaleID").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let initFList = function () {
            $("#tbFactor").bsDataTables({
                columns: columns_f,
                url: '/measuringroom/scaleFactor/list',
                columnDefs: columnDefs_f,
                paging: false,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_f(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "data": jsonObj,
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        };
        //条目排序
        let moveF = function (id, e) {
            let jsonObj = {};
            jsonObj.factorId = id;
            jsonObj.flag = e;
            $.post("/measuringroom/scaleFactor/sort", jsonObj, function (res) {
                if (res.resultCode === 200) {
                    oTable.draw();
                    layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            }, 'json');
        };
        //结果解释
        let oTableFE = null;
        let initFEList = function () {
            if (oTableFE != null) {
                oTableFE.destroy();
            }
            oTableFE = $("#tbFE").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "sAjaxSource": "/measuringroom/factorExplain/list",
                "fnServerData": retrieveDataFE, //执行方法
                //配置列要显示的数据
                "columns": [
                    { "data": "startValue", "bSortable": false },
                    { "data": "endValue", "bSortable": false },
                    {
                        "data": "warningLevel", "render":
                            function (data, type, full, meta) {
                                let warningLevelLabel = '无';
                                if(full.warningLevel != undefined) {
                                    switch (full.warningLevel) {
                                        case 0:
                                            warningLevelLabel = '无';
                                            break;
                                        case 1:
                                            warningLevelLabel = '绿码';
                                            break;
                                        case 2:
                                            warningLevelLabel = '蓝码';
                                            break;
                                        case 3:
                                            warningLevelLabel = '黄码';
                                            break;
                                        case 4:
                                            warningLevelLabel = '橙码';
                                            break;
                                        case 5:
                                            warningLevelLabel = '红码';
                                            break;
                                    }
                                }
                                return warningLevelLabel;
                            }, "bSortable": false},
                    { "data": "interpretation", "bSortable": false}],
                "columnDefs":[{
                    targets: 4,
                    render: function (data, type, row, meta) {
                        return '<button class="btn btn-outline-warning btn-sm editFE mr-1 mb-1"><i class="fa fa-pencil-square-o mr-1"></i>编辑</button><button class="btn btn-outline-danger btn-sm delFE"><i class="fa fa-trash-o mr-1"></i>删除</button>'
                    }
                }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        let getQueryCondition_fe = function (data) {
            let param = {};
            param.factorId = $("#hidFactorID").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.iDisplayLength = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.iDisplayStart = item.value;
                }
            });
            return param;
        };
        let retrieveDataFE = function (sSource, aoData, fnCallback) {
            $.ajax({
                "type": "post",
                "url": sSource,
                "dataType": "json",
                "data": getQueryCondition_fe(aoData),
                "success": function (res) {
                    fnCallback(res);
                }
            });
        };
        //异常条件
        let oTableAC = null;
        let initACList = function () {
            if (oTableAC != null) {
                oTableAC.destroy();
            }
            oTableAC = $("#tbAC").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "sAjaxSource": "/measuringroom/factorac/list",
                "fnServerData": retrieveDataAC, //执行方法
                //配置列要显示的数据
                "columns": [{ "data": "sexCondition", "bSortable": false }],
                "columnDefs": [{
                    targets: 1, render: function (data, type, row, meta) {
                        let ageCondition = row.ageCondition;
                        if (row.ageValue != undefined) {
                            switch (ageCondition) {
                                case 1:
                                    ageCondition = "&gt;";
                                    break;
                                case 2:
                                    ageCondition = "&gt;=";
                                    break;
                                case 3:
                                    ageCondition = "&lt;";
                                    break;
                                case 4:
                                    ageCondition = "&lt;=";
                                    break;
                                case 5:
                                    ageCondition = "=";
                                    break;
                            }
                            return ageCondition + row.ageValue;
                        }
                        else {
                            return "";
                        }
                    }
                },
                    {
                        targets: 2, render: function (data, type, row, meta) {
                            let scoreCondition = row.scoreCondition;
                            if (row.scoreValue != undefined) {
                                switch (scoreCondition) {
                                    case 1:
                                        scoreCondition = "&gt;";
                                        break;
                                    case 2:
                                        scoreCondition = "&gt;=";
                                        break;
                                    case 3:
                                        scoreCondition = "&lt;";
                                        break;
                                    case 4:
                                        scoreCondition = "&lt;=";
                                        break;
                                    case 5:
                                        scoreCondition = "=";
                                        break;
                                }
                                return scoreCondition + row.scoreValue;
                            }
                            else {
                                return "";
                            }
                        }
                    },
                    {
                        targets: 3, render: function (data, type, row, meta) {
                            return '<button class="btn btn-outline-danger btn-sm delAC"><i class="fa fa-trash-o mr-1"></i>删除</button>'
                        }
                    }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        let getQueryCondition_ac = function (data) {
            let param = {};
            param.factorId = $("#hidFactorID").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.iDisplayLength = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.iDisplayStart = item.value;
                }
            });
            return param;
        };
        let retrieveDataAC = function (sSource, aoData, fnCallback) {
            $.ajax({
                "type": "post",
                "url": sSource,
                "dataType": "json",
                "data": getQueryCondition_ac(aoData),
                "success": function (res) {
                    fnCallback(res);
                }
            });
        };
        /*S3 因子设置 end */

        let complete = function () {
            let url = "/measuringroom/scale/done";
            let jsonObj = { scaleId: $("#hidScaleID").val(), state: 1 };
            $.post(url, jsonObj, function (res) {
                if (res.resultCode === 200) {
                    layer.alert(res.resultMsg, {
                        icon: 1, yes: function (index) {
                            location.href = "/measuringroom/scale/list";
                        }
                    });
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            });
        };
    </script>
</th:block>
</body>
</html>