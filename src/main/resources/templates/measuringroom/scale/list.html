<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <style>
        #scaleIntro img {
            width: 100%;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">量表管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">量表库</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="sr-scaleType" id="sr-scaleType">
                                        <option></option>
                                    </select>
                                </div>
                                <div class="form-group mb-2 mr-1">
                                    <input type="text" class="form-control" id="sr-scaleName" placeholder="量表名称..." autocomplete="off">
                                </div>
                                <button type="button" class="btn btn-primary mb-2" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <a id="btnAdd" th:href="@{/measuringroom/scale/get_undone_scale}" class="btn btn-success mb-2 mr-1" title="创建量表"><i class="fa fa-plus mr-1"></i>创建量表</a>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1 mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbScale" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>名称</th>
                                <th>类型</th>
                                <th>题数</th>
                                <th>推荐</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.量表管理 start -->
    <div id="scale-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-info-circle mr-1"></i> 量表信息</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmScale" class="form-inline pl-2 pr-2" action="#">
                    <div class="modal-body">
                        <ul class="nav nav-tabs nav-bordered nav-justified mb-2">
                            <li class="nav-item">
                                <a href="#baseInfo" data-toggle="tab" aria-expanded="false" class="nav-link active">
                                    <span class="d-lg-block">基本信息</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#intro" data-toggle="tab" aria-expanded="false" class="nav-link">
                                    <span class="d-lg-block">量表介绍</span>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active mt-3" id="baseInfo">
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">量表类型：</label>
                                    <div class="col-sm-9">
                                        <span id="scaleType"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">量表名称：</label>
                                    <div class="col-sm-9">
                                        <span id="scaleName"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">量表别名：</label>
                                    <div class="col-sm-9">
                                        <span id="scaleAlias"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">指导语：</label>
                                    <div class="col-sm-9">
                                        <span id="scaleGuide"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">作答时间：</label>
                                    <div class="col-sm-9">
                                        <span id="needTime"></span>分钟
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">限制条件：</label>
                                    <div class="col-sm-9">
                                        <span id="limit"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">年龄限制：</label>
                                    <div class="col-sm-9">
                                        <span id="age_limit"></span>
                                    </div>
                                </div>
                                <div class="form-group mb-2">
                                    <label class="col-sm-3">是否推荐：</label>
                                    <div class="col-sm-9">
                                        <span id="isRecommend"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="intro">
                                <div class="form-control-plaintext text-secondary" id="scaleIntro"></div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.量表管理 end -->
    <!-- modal.量表排序 start-->
    <div id="order-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmOrder" action="#">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title">设置量表排序</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>排序</label>
                            <input class="form-control" type="text" id="order" name="order" placeholder="请输入排序数字" />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSetOrder" class="btn btn-primary btn-sm" value="保存" />
                        <input type="hidden" id="hidScaleID" value="" />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.量表排序 end-->
    <!-- 配置量表测评报告图表 start -->
    <div id="chart-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmChartType" action="#">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title">配置量表测评报告图表类型</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="factorType">因子类型</label>
                            <select class="form-control" name="factorType" id="factorType">
                                <option value="">请选择因子类型</option>
                                <option value="1">普通因子</option>
                                <option value="2">复合因子</option>
                            </select>
                        </div>
                        <div class="form-group hide" id="div-muti">
                            <label for="chartTypeMuti">图表类型</label>
                            <select class="form-control select2 chart-select"  multiple data-toggle="select2" name="chartTypeMuti" id="chartTypeMuti">
                                <option value="line">折线图</option>
                                <option value="spline">平滑曲线图</option>
                                <option value="column">柱状图</option>
                                <option value="bar">条形图</option>
                                <option value="pie">饼图</option>
                                <option value="radar">雷达图</option>
                                <option value="spiderweb">蜘蛛图</option>
                                <option value="columnpyramid">金字塔图</option>
                                <option value="waterfall">瀑布图</option>
                            </select>
                        </div>
                        <div class="form-group hide" id="div-single">
                            <label for="chartTypeSingle">图表类型</label>
                            <select class="form-control select2 chart-select" multiple data-toggle="select2" name="chartTypeSingle" id="chartTypeSingle">
                                <option value="gauge">速度仪图（1）</option>
                                <option value="solidgauge">速度仪（2）</option>
                                <option value="line">折线图</option>
                                <option value="spline">平滑曲线图</option>
                                <option value="column">柱状图</option>
                                <option value="bar">条形图</option>
                                <option value="pie">饼图</option>
                            </select>
                        </div>
                        <div class="form-group hide" id="chart-example-group">
                            <label>图表示例：</label>
                            <div id="chart-example">
                                <img src="/static/images/nopic.png" id="chartExample" class="img-fluid">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveChartType" class="btn btn-primary btn-sm" value="保存" />
                        <input type="hidden" name="hidChartType" id="hidChartType" value="">
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 配置量表测评报告图表 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbScale').DataTable().ajax.reload();
            });
            //datatables
            $("#tbScale").bsDataTables({
                columns: columns,
                url: '/measuringroom/scale/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (data) {
                            let res = JSON.parse(data);
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //量表信息
            $("#tbScale").on('click', '.info', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#scaleType").html(data.scaleTypeName);
                $("#scaleName").html(data.scaleName);
                $("#scaleAlias").html(data.scaleAlias);
                $("#scaleGuide").html(data.scaleGuide);
                $("#scaleIntro").html(data.scaleIntro);
                $("#needTime").html(data.needTime);
                $("#limit").html(data.testLimit.substring(0, data.testLimit.lastIndexOf(',')));
                let ageLimit = data.ageLimit.split(',');
                if (ageLimit[0] === "0" && ageLimit[1] === "0") {
                    $("#age_limit").html("无限制");
                }
                else {
                    $("#age_limit").html("" + ageLimit[0] + "岁" + "-" + "" + ageLimit[1] + "岁");
                }
                if (data.isRecommend === 1) {
                    $("#isRecommend").html("是")
                }
                else {
                    $("#isRecommend").html("否");
                }
                $("#scale-modal").modal();
            });

            //修改
            $("#tbScale").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = { scaleId: data.id, state: 0 };
                $.post("/measuringroom/scale/done", jsonObj, function (res) {
                    if (res.resultCode === 200) {
                        location.href = "/measuringroom/scale/get_undone_scale";
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            });
            //设置排序
            $("#tbScale").on('click', '.order', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidScaleID").val(data.id);
                $("#order").val(data.sort);
                $("#order-modal").modal();
            });
            //快速测试
            $("#tbScale").on('click', '.measuring', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/measuringroom/testing/guide?type=3&scaleId=" + data.id;
            });
            //删除(批量)
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids;
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/measuringroom/scale/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#tbScale").on('click', '.export', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href="/measuringroom/scale/export?scaleId="+data.id;
            });
            $("#tbScale").on('click', '.chartType', function () {
                layer.msg('请稍后…', {
                    icon: 17, shade: 0.2, time: false
                });
                let data = oTable.row($(this).parents('tr')).data();
                let charts = data.listCharts;
                $("#hidScaleID").val(data.id);
                $.post('/measuringroom/scaleFactor/get_factor_count', { scaleId: data.id }, function (res) {
                    layer.closeAll();
                    if(res > 0){
                        // 先重置表单状态
                        $("#div-muti").addClass("hide");
                        $("#div-single").addClass("hide");
                        $("#chart-example-group").addClass('hide');

                        // 显示当前图表配置
                        loadCurrentChartConfig(charts);

                        $("#chart-modal").modal();
                    }
                    else{
                        layer.msg("该量表还没有配置因子，无法进行此操作！", { icon: 2, time: 2000 });
                    }
                });
            });

            // 存储当前量表的图表配置
            let currentCharts = [];

            // 加载当前量表的图表配置并显示在模态框中
            function loadCurrentChartConfig(charts) {
                currentCharts = charts || []; // 保存当前量表的图表配置到全局变量

                if(!charts || charts.length === 0) {
                    return; // 没有配置，保持默认状态
                }

                // 按因子类型分组当前量表的图表配置
                let normalFactorCharts = charts.filter(chart => chart.factorType === 1);
                let complexFactorCharts = charts.filter(chart => chart.factorType === 2);

                // 处理旧数据（没有factorType字段的）
                let oldCharts = charts.filter(chart => chart.factorType === undefined || chart.factorType === null);
                if(oldCharts.length > 0) {
                    normalFactorCharts = oldCharts; // 将旧配置作为普通因子配置
                }

                // 如果有普通因子配置，默认显示普通因子选项卡和对应的图表类型
                if(normalFactorCharts.length > 0) {
                    $("#factorType").val('1').trigger('change');
                    setTimeout(function() {
                        let chartTypes = normalFactorCharts.map(chart => chart.chartType);
                        $("#chartTypeSingle").val(chartTypes).trigger('change');
                    }, 100);
                }
                // 如果只有复合因子配置，显示复合因子选项卡和对应的图表类型
                else if(complexFactorCharts.length > 0) {
                    $("#factorType").val('2').trigger('change');
                    setTimeout(function() {
                        let chartTypes = complexFactorCharts.map(chart => chart.chartType);
                        $("#chartTypeMuti").val(chartTypes).trigger('change');
                    }, 100);
                }
            }

            // 从当前量表的图表配置中筛选指定因子类型的图表类型并显示
            function showChartTypesByFactorType(factorType) {
                if(!currentCharts || currentCharts.length === 0) {
                    return;
                }

                let targetCharts = [];
                if(factorType === '1') {
                    // 普通因子
                    targetCharts = currentCharts.filter(chart => chart.factorType === 1);
                    // 处理旧数据
                    if(targetCharts.length === 0) {
                        let oldCharts = currentCharts.filter(chart => chart.factorType === undefined || chart.factorType === null);
                        targetCharts = oldCharts;
                    }
                    if(targetCharts.length > 0) {
                        let chartTypes = targetCharts.map(chart => chart.chartType);
                        $("#chartTypeSingle").val(chartTypes).trigger('change');
                    }
                } else if(factorType === '2') {
                    // 复合因子
                    targetCharts = currentCharts.filter(chart => chart.factorType === 2);
                    if(targetCharts.length > 0) {
                        let chartTypes = targetCharts.map(chart => chart.chartType);
                        $("#chartTypeMuti").val(chartTypes).trigger('change');
                    }
                }
            }
            // 因子类型选择事件
            $('#factorType').on('change', function() {
                let factorType = $(this).val();
                if(factorType === '1') {
                    // 普通因子 - 显示普通因子图表类型选择
                    $("#div-single").removeClass("hide");
                    $("#div-muti").addClass("hide");
                    $("#hidChartType").val(3); // 3表示普通因子多选
                    // 清空当前选择
                    $("#chartTypeSingle").val('').trigger('change');
                    // 显示该因子类型的图表配置
                    setTimeout(function() {
                        showChartTypesByFactorType('1');
                    }, 50);
                } else if(factorType === '2') {
                    // 复合因子 - 显示复合因子图表类型选择
                    $("#div-muti").removeClass("hide");
                    $("#div-single").addClass("hide");
                    $("#hidChartType").val(1); // 1表示复合因子多选
                    // 清空当前选择
                    $("#chartTypeMuti").val('').trigger('change');
                    // 显示该因子类型的图表配置
                    setTimeout(function() {
                        showChartTypesByFactorType('2');
                    }, 50);
                } else {
                    // 未选择 - 隐藏所有图表类型
                    $("#div-muti").addClass("hide");
                    $("#div-single").addClass("hide");
                    $("#hidChartType").val('');
                    // 只有在未选择时才清空图表选择
                    $(".chart-select").val('').trigger("change");
                    // 隐藏图表示例
                    $("#chart-example-group").addClass('hide');
                }
            });

            $('#chart-modal').on('hidden.bs.modal', function () {
                // 清空所有select元素的选中项
                $(".chart-select").val('').trigger("change");
                $("#factorType").val('').trigger("change");
                // 重置图片为默认图片并隐藏图表示例
                $('#chartExample').attr('src', '/static/images/nopic.png');
                $("#chart-example-group").addClass('hide');
                // 清空全局变量
                currentCharts = [];
            });
            $('.chart-select').on('change',function(){
                let selectedValues = $(this).val(); // 获取所有选中的值
                if(($("#hidChartType").val() === '1' || $("#hidChartType").val() === '3') && selectedValues && selectedValues.length >= 1){
                    let lastSelectedValue = selectedValues[selectedValues.length - 1]; // 获取最后一个选中的值
                    $('#chartExample').attr('src', '/static/images/charts/'+lastSelectedValue+'.png');
                    $("#chart-example-group").removeClass('hide'); // 显示图表示例
                }
                else {
                    $('#chartExample').attr('src', '/static/images/nopic.png'); // 重置为默认图片
                    $("#chart-example-group").addClass('hide'); // 隐藏图表示例
                }
            })
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
            $("#frmOrder").validate({
                rules: {
                    sort: { required: true }
                },
                messages: {
                    sort: { required: "请填写排序数字" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        scaleId: $("#hidScaleID").val(),
                        sort: $.trim($("#order").val())
                    };
                    $("#btnSetOrder").val("请稍后……");
                    $("#btnSetOrder").attr("Disabled", true);
                    $.ajax({
                        "type": "post",
                        "url": '/measuringroom/scale/set_sort',
                        "dataType": "json",
                        "data": jsonObj,
                        "success": function (res) {
                            $("#btnSetOrder").val("保存");
                            $("#btnSetOrder").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#order-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });

            $("#frmChartType").validate({
                rules: {
                    factorType: {
                        required: true
                    },
                    chartTypeMuti: {
                        required: function(element) {
                            return $("#div-muti").is(":visible");
                        },
                        maxlength: 2
                    },
                    chartTypeSingle: {
                        required: function(element) {
                            return $("#div-single").is(":visible");
                        },
                        maxlength: 2
                    }
                },
                messages: {
                    factorType: {
                        required: "请选择因子类型"
                    },
                    chartTypeMuti: {
                        required: "请选择至少一个图表类型",
                        maxlength: "最多可以选择两个图表类型"
                    },
                    chartTypeSingle: {
                        required: "请选择至少一个图表类型",
                        maxlength: "最多可以选择两个图表类型"
                    }
                },
                submitHandler: function () {
                    let selectCharts = "";
                    let factorType = $("#factorType").val();

                    if ($("#div-muti").is(":visible")) {
                        selectCharts = $("#chartTypeMuti").val().join(",");
                    }
                    if($("#div-single").is(":visible")) {
                        let singleCharts = $("#chartTypeSingle").val();
                        selectCharts = Array.isArray(singleCharts) ? singleCharts.join(",") : singleCharts;
                    }
                    let jsonObj = {
                        scaleId: $("#hidScaleID").val(),
                        factorType: factorType,
                        charts: selectCharts
                    };
                    $("#btnSaveChartType").val("请稍后……");
                    $("#btnSaveChartType").attr("Disabled", true);
                    $.ajax({
                        "type": "post",
                        "url": '/measuringroom/scalecharts/save',
                        "dataType": "json",
                        "data": jsonObj,
                        "success": function (res) {
                            $("#btnSaveChartType").val("保存");
                            $("#btnSaveChartType").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#chart-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            initSelect("#sr-scaleType", "/measuringroom/scaletype/get_for_select", "" ,"","选择量表类型");
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "scaleName", "bSortable": false },
            { "data": "scaleTypeName", "bSortable": false },
            { "data": "qCount", "bSortable": false },
            {
                "data": "isRecommend", "render":
                    function (data, type, full, meta) {
                        if (full.isRecommend === 1) {
                            return '<span class="badge badge-success badge-pill">是</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill">否</span>';
                        }
                    }, "bSortable": false }
        ];
        let columnDefs = [{
            targets: 5, render: function (data, type, row, meta) {
                let buttons = '<button type="button" class="btn btn-outline-primary btn-sm mr-1 info"><i class="fa fa-info-circle mr-1"></i>基本信息</button>';
                if ('[[${canDoTest}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-primary btn-sm mr-1 measuring"><i class="mdi mdi-arrow-right-circle mr-1"></i>快速测试</button>';
                if ('[[${canUpdate}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canExport}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-info btn-sm export mr-1"><i class="fa fa-file-word-o mr-1"></i>导出</button>';
                if ('[[${canSetOrder}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-info btn-sm order mr-1"><i class="fa fa-arrows-v mr-1"></i>设置排序</button>';
                if ('[[${canUpdate}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-info btn-sm chartType"><i class="fa fa-line-chart mr-1"></i>配置报告图表</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.scaleName = $("#sr-scaleName").val();
            param.scaleTypeId = $("#sr-scaleType").val();
            param.isDone = 1;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>